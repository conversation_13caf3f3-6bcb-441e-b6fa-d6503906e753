"use client"

import React, { useState, useEffect } from 'react'
import { Plus, Package, Key, AlertCircle, Upload, Save, Trash2, Settings, List } from 'lucide-react'
import { useAdminCRUD } from '../hooks/useAdminCRUD'
import { useAdminModal } from '../hooks/useAdminModal'
import AdminTable, { ColumnDefinition } from './shared/AdminTable'
import AdminPagination from './shared/AdminPagination'
import AdminFilters, { FilterOption } from './shared/AdminFilters'
import AdminModal from './shared/AdminModal'
import { useTenant } from '../../contexts/TenantContext'
import { getCategories } from '../../lib/categories'

interface Product {
  id: string
  slug: string
  title: string
  description: string
  cover_image: string
  category_id?: string
  tags: string[]
  rating: number
  comment_count: number
  featured: boolean
  // Product-level pricing (used when no packages exist)
  original_price?: number
  user_price?: number
  discount_price?: number
  distributor_price?: number
  created_at: string
  updated_at: string
  packages: Package[]
  categories?: { id: string; name: string; slug: string }
}

interface DigitalCode {
  id: string
  key: string
  used: boolean
  assignedToOrderId: string | null
  assignedAt?: string
}

interface Package {
  id?: string
  name: string
  // Legacy price field for backward compatibility
  price?: number
  // Enhanced pricing fields
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number
  image?: string
  description?: string
  use_product_image: boolean
  image_reference_type: 'url' | 'product_image'
  has_digital_codes: boolean
  digitalCodes?: DigitalCode[]
  hasDigitalCodes?: boolean
  availableCodesCount?: number
  // Manual inventory management fields
  manual_quantity?: number
  track_inventory?: boolean
  unlimited_stock?: boolean
  // Enhanced custom fields
  customFields?: EnhancedCustomField[]
  dropdowns?: EnhancedDropdown[]
}

interface EnhancedCustomField {
  id?: string
  label: string
  field_type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea'
  description?: string
  required: boolean
  placeholder?: string
  field_order: number
  validation_rules?: {
    pattern?: string
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
  }
  display_options?: {
    icon?: string
    helpText?: string
    showStrength?: boolean
    rows?: number
  }
}

interface EnhancedDropdown {
  id?: string
  label: string
  description?: string
  required: boolean
  field_order: number
  validation_rules?: {
    allowMultiple?: boolean
    minSelections?: number
    maxSelections?: number
  }
  display_options?: {
    icon?: string
    helpText?: string
    searchable?: boolean
    placeholder?: string
  }
  options: DropdownOption[]
}

interface DropdownOption {
  id?: string
  label: string
  description?: string
  display_order: number
  is_default: boolean
}

interface Category {
  id: string
  name: string
  slug: string
}

// Sub-component for basic product information
const ProductBasicForm = ({ formData, setFormData, categories, categoriesLoading }: {
  formData: Partial<Product>
  setFormData: React.Dispatch<React.SetStateAction<Partial<Product>>>
  categories: Category[]
  categoriesLoading: boolean
}) => (
  <div className="space-y-4 md:space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
      <div>
        <label className="block text-sm font-medium mb-2">عنوان المنتج *</label>
        <input
          type="text"
          value={formData.title || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          placeholder="أدخل عنوان المنتج"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">الفئة *</label>
        {categoriesLoading ? (
          <div className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 animate-pulse">
            جاري التحميل...
          </div>
        ) : (
          <select
            value={formData.category_id || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, category_id: e.target.value }))}
            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
          >
            <option value="">اختر فئة المنتج</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        )}
      </div>
    </div>

    <div>
      <label className="block text-sm font-medium mb-2">وصف المنتج *</label>
      <textarea
        value={formData.description || ''}
        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        rows={3}
        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
        placeholder="أدخل وصف المنتج"
      />
    </div>

    <div>
      <label className="block text-sm font-medium mb-2">صورة الغلاف *</label>
      <input
        type="url"
        value={formData.cover_image || ''}
        onChange={(e) => setFormData(prev => ({ ...prev, cover_image: e.target.value }))}
        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
        placeholder="رابط صورة الغلاف"
      />
    </div>

    <div>
      <label className="block text-sm font-medium mb-2">الكلمات المفتاحية</label>
      <input
        type="text"
        value={formData.tags?.join(', ') || ''}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
        }))}
        className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
        placeholder="أدخل الكلمات المفتاحية مفصولة بفواصل"
      />
    </div>

    <div className="flex items-center">
      <input
        type="checkbox"
        id="featured"
        checked={formData.featured || false}
        onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
        className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
      />
      <label htmlFor="featured" className="mr-2 text-sm font-medium">
        منتج مميز
      </label>
    </div>
  </div>
)

export default function ProductManagement() {
  const { tenant } = useTenant()
  const [categories, setCategories] = useState<Category[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(false)
  const [formData, setFormData] = useState<Partial<Product>>({
    title: '',
    description: '',
    category_id: '',
    tags: [],
    cover_image: '',
    packages: [],
    featured: false,
    // Product-level pricing
    original_price: undefined,
    user_price: undefined,
    discount_price: undefined,
    distributor_price: undefined
  })

  // Enhanced custom fields state
  const [productCustomFields, setProductCustomFields] = useState<EnhancedCustomField[]>([])
  const [productDropdowns, setProductDropdowns] = useState<EnhancedDropdown[]>([])
  const [activeTab, setActiveTab] = useState<'basic' | 'pricing' | 'packages' | 'fields'>('basic')

  // CRUD operations
  const {
    items: products,
    loading,
    actionLoading,
    pagination,
    filters,
    fetchItems,
    createItem,
    updateItem,
    deleteItem,
    goToPage,
    nextPage,
    prevPage,
    updateFilters
  } = useAdminCRUD<Product>({
    entityType: 'products',
    apiEndpoint: '/api/admin/products',
    cacheKey: 'admin-products'
  })

  // Modal management
  const {
    isOpen: isModalOpen,
    mode: modalMode,
    item: editingProduct,
    openCreateModal,
    openEditModal,
    closeModal
  } = useAdminModal<Product>()

  // Load categories
  useEffect(() => {
    if (tenant) {
      loadCategories()
    }
  }, [tenant])

  const loadCategories = async () => {
    if (!tenant) return
    setCategoriesLoading(true)
    try {
      const result = await getCategories(tenant.id)
      if (result.success && result.data) {
        setCategories(result.data)
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    } finally {
      setCategoriesLoading(false)
    }
  }

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isModalOpen) {
      if (modalMode === 'edit' && editingProduct) {
        setFormData({
          title: editingProduct.title,
          description: editingProduct.description,
          category_id: editingProduct.category_id,
          tags: editingProduct.tags || [],
          cover_image: editingProduct.cover_image,
          packages: editingProduct.packages || [],
          featured: editingProduct.featured,
          // Product-level pricing
          original_price: editingProduct.original_price,
          user_price: editingProduct.user_price,
          discount_price: editingProduct.discount_price,
          distributor_price: editingProduct.distributor_price
        })

        // Load existing custom fields and dropdowns
        setProductCustomFields(editingProduct.customFields || [])
        setProductDropdowns(editingProduct.dropdowns || [])
      } else {
        setFormData({
          title: '',
          description: '',
          category_id: '',
          tags: [],
          cover_image: '',
          packages: [],
          featured: false,
          // Product-level pricing
          original_price: undefined,
          user_price: undefined,
          discount_price: undefined,
          distributor_price: undefined
        })

        // Reset custom fields and dropdowns for new product
        setProductCustomFields([])
        setProductDropdowns([])
        setActiveTab('basic')
      }
    }
  }, [isModalOpen, modalMode, editingProduct])

  // Table columns definition
  const columns: ColumnDefinition<Product>[] = [
    {
      key: 'title',
      label: 'المنتج',
      render: (product) => (
        <div className="flex items-center space-x-3 space-x-reverse">
          <img
            src={product.cover_image || "/logo.jpg"}
            alt={product.title}
            className="w-12 h-12 rounded-lg object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = "/logo.jpg"
            }}
          />
          <div>
            <p className="font-semibold">{product.title}</p>
            <p className="text-sm text-gray-400">{product.slug}</p>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'الفئة',
      render: (product) => product.categories?.name || product.category || 'غير محدد'
    },
    {
      key: 'packages',
      label: 'الحزم',
      render: (product) => `${product.packages?.length || 0} حزمة`
    },
    {
      key: 'digital_codes',
      label: 'الأكواد الرقمية',
      render: (product) => {
        const totalCodes = product.packages?.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0) || 0
        const availableCodes = product.packages?.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0) || 0
        const hasDigitalPackages = product.packages?.some(pkg => pkg.hasDigitalCodes || pkg.has_digital_codes)

        if (!hasDigitalPackages) {
          return <span className="text-gray-500 text-sm">لا يوجد</span>
        }
        return (
          <div className="flex items-center space-x-2 space-x-reverse">
            <Key className="w-4 h-4 text-blue-400" />
            <span className="text-sm">{availableCodes}/{totalCodes}</span>
          </div>
        )
      }
    },
    {
      key: 'inventory',
      label: 'المخزون',
      render: (product) => {
        const manualInventoryPackages = product.packages?.filter(pkg => pkg.track_inventory) || []
        const digitalPackages = product.packages?.filter(pkg => pkg.hasDigitalCodes || pkg.has_digital_codes) || []

        if (manualInventoryPackages.length === 0 && digitalPackages.length === 0) {
          return <span className="text-gray-500 text-sm">غير محدد</span>
        }

        const inventoryItems = []

        // Show manual inventory status
        if (manualInventoryPackages.length > 0) {
          const totalManualStock = manualInventoryPackages.reduce((sum, pkg) => {
            if (pkg.unlimited_stock) return sum + 999999
            return sum + (pkg.manual_quantity || 0)
          }, 0)

          const hasUnlimited = manualInventoryPackages.some(pkg => pkg.unlimited_stock)

          inventoryItems.push(
            <div key="manual" className="flex items-center space-x-1 space-x-reverse">
              <Package className="w-3 h-3 text-green-400" />
              <span className="text-xs">
                {hasUnlimited ? 'غير محدود' : `${totalManualStock} قطعة`}
              </span>
            </div>
          )
        }

        // Show digital codes status
        if (digitalPackages.length > 0) {
          const totalCodes = digitalPackages.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0)
          const availableCodes = digitalPackages.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0)

          inventoryItems.push(
            <div key="digital" className="flex items-center space-x-1 space-x-reverse">
              <Key className="w-3 h-3 text-blue-400" />
              <span className="text-xs">{availableCodes} كود</span>
            </div>
          )
        }

        return (
          <div className="space-y-1">
            {inventoryItems}
          </div>
        )
      }
    },
    {
      key: 'rating',
      label: 'التقييم',
      render: (product) => (
        <div className="flex items-center space-x-1 space-x-reverse">
          <span>{product.rating}</span>
          <span className="text-gray-400">({product.comment_count})</span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'الحالة',
      render: (product) => (
        <div className="flex space-x-2 space-x-reverse">
          {product.featured && (
            <span className="px-2 py-1 bg-purple-400/10 text-purple-400 rounded-xl text-xs">مميز</span>
          )}
          {product.popular && (
            <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded-xl text-xs">شائع</span>
          )}
        </div>
      )
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: 'الفئة',
      type: 'select',
      options: categories.map(cat => ({ value: cat.name, label: cat.name }))
    },
    {
      key: 'featured',
      label: 'مميز',
      type: 'boolean'
    },
    {
      key: 'popular',
      label: 'شائع',
      type: 'boolean'
    }
  ]

  // Mobile card renderer
  const renderMobileCard = (product: Product) => (
    <div>
      <div className="flex items-start space-x-3 space-x-reverse mb-3">
        <img
          src={product.cover_image || "/logo.jpg"}
          alt={product.title}
          className="w-16 h-16 rounded-xl object-cover flex-shrink-0"
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.src = "/logo.jpg"
          }}
        />
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-lg truncate">{product.title}</h3>
          <p className="text-sm text-gray-400 truncate">{product.slug}</p>
          <div className="flex items-center space-x-2 space-x-reverse mt-2">
            {product.featured && (
              <span className="px-2 py-1 bg-purple-400/10 text-purple-400 rounded-lg text-xs">مميز</span>
            )}
            {product.popular && (
              <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded-lg text-xs">شائع</span>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3 md:gap-4">
        <div>
          <p className="text-xs text-gray-400 mb-1">الفئة</p>
          <p className="text-sm font-medium">{product.categories?.name || product.category || 'غير محدد'}</p>
        </div>
        <div>
          <p className="text-xs text-gray-400 mb-1">الحزم</p>
          <p className="text-sm font-medium">{product.packages?.length || 0} حزمة</p>
        </div>
        <div>
          <p className="text-xs text-gray-400 mb-1">التقييم</p>
          <div className="flex items-center space-x-1 space-x-reverse">
            <span className="text-sm font-medium">{product.rating}</span>
            <span className="text-xs text-gray-400">({product.comment_count})</span>
          </div>
        </div>
        <div>
          <p className="text-xs text-gray-400 mb-1">الأكواد الرقمية</p>
          {(() => {
            const totalCodes = product.packages?.reduce((sum, pkg) => sum + (pkg.digitalCodes?.length || 0), 0) || 0
            const availableCodes = product.packages?.reduce((sum, pkg) => sum + (pkg.availableCodesCount || 0), 0) || 0
            const hasDigitalPackages = product.packages?.some(pkg => pkg.hasDigitalCodes || pkg.has_digital_codes)

            if (!hasDigitalPackages) {
              return <span className="text-gray-500 text-sm">لا يوجد</span>
            }
            return (
              <div className="flex items-center space-x-2 space-x-reverse">
                <Key className="w-3 h-3 text-blue-400" />
                <span className="text-sm font-medium">{availableCodes}/{totalCodes}</span>
              </div>
            )
          })()}
        </div>
      </div>
    </div>
  )

  // Package management functions
  const addPackage = () => {
    const newPackage: Package = {
      id: Date.now().toString(),
      name: "",
      original_price: 0,
      user_price: 0,
      discount_price: undefined,
      distributor_price: undefined,
      image: "",
      use_product_image: true,
      image_reference_type: "product_image",
      has_digital_codes: false,
      hasDigitalCodes: false,
      digitalCodes: [],
      availableCodesCount: 0,
      // Default inventory settings for new packages - unlimited stock by default
      manual_quantity: 0,
      track_inventory: true,
      unlimited_stock: true
    }
    setFormData(prev => ({
      ...prev,
      packages: [...(prev.packages || []), newPackage]
    }))
  }

  const updatePackage = (index: number, field: keyof Package, value: any) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.map((pkg, i) => (i === index ? { ...pkg, [field]: value } : pkg)) || []
    }))
  }

  const removePackage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.filter((_, i) => i !== index) || []
    }))
  }

  // Enhanced Custom Fields Management
  const addProductCustomField = () => {
    // Find the next available order number
    const existingOrders = productCustomFields.map(f => f.field_order)
    const nextOrder = existingOrders.length > 0 ? Math.max(...existingOrders) + 1 : 0

    const newField: EnhancedCustomField = {
      id: Date.now().toString(),
      label: '',
      field_type: 'text',
      description: '',
      required: false,
      placeholder: '',
      field_order: nextOrder,
      validation_rules: {},
      display_options: {}
    }
    setProductCustomFields(prev => [...prev, newField])
  }

  const updateProductCustomField = (index: number, field: keyof EnhancedCustomField, value: any) => {
    setProductCustomFields(prev =>
      prev.map((customField, i) => (i === index ? { ...customField, [field]: value } : customField))
    )
  }

  const removeProductCustomField = (index: number) => {
    setProductCustomFields(prev => prev.filter((_, i) => i !== index))
  }

  // Enhanced Dropdowns Management
  const addProductDropdown = () => {
    // Find the next available order number
    const existingOrders = productDropdowns.map(d => d.field_order)
    const nextOrder = existingOrders.length > 0 ? Math.max(...existingOrders) + 1 : 0

    const newDropdown: EnhancedDropdown = {
      id: Date.now().toString(),
      label: '',
      description: '',
      required: false,
      field_order: nextOrder,
      validation_rules: {},
      display_options: {},
      options: []
    }
    setProductDropdowns(prev => [...prev, newDropdown])
  }

  const updateProductDropdown = (index: number, field: keyof EnhancedDropdown, value: any) => {
    setProductDropdowns(prev =>
      prev.map((dropdown, i) => (i === index ? { ...dropdown, [field]: value } : dropdown))
    )
  }

  const removeProductDropdown = (index: number) => {
    setProductDropdowns(prev => prev.filter((_, i) => i !== index))
  }

  const addDropdownOption = (dropdownIndex: number) => {
    const newOption: DropdownOption = {
      id: Date.now().toString(),
      label: '',
      description: '',
      display_order: productDropdowns[dropdownIndex]?.options.length || 0,
      is_default: false
    }
    setProductDropdowns(prev =>
      prev.map((dropdown, i) =>
        i === dropdownIndex
          ? { ...dropdown, options: [...dropdown.options, newOption] }
          : dropdown
      )
    )
  }

  const updateDropdownOption = (dropdownIndex: number, optionIndex: number, field: keyof DropdownOption, value: any) => {
    setProductDropdowns(prev =>
      prev.map((dropdown, i) =>
        i === dropdownIndex
          ? {
              ...dropdown,
              options: dropdown.options.map((option, j) =>
                j === optionIndex ? { ...option, [field]: value } : option
              )
            }
          : dropdown
      )
    )
  }

  const removeDropdownOption = (dropdownIndex: number, optionIndex: number) => {
    setProductDropdowns(prev =>
      prev.map((dropdown, i) =>
        i === dropdownIndex
          ? { ...dropdown, options: dropdown.options.filter((_, j) => j !== optionIndex) }
          : dropdown
      )
    )
  }

  // Helper function for profit margin calculation
  const calculateProfitMargin = (cost: number, price: number): number => {
    return price - cost
  }

  const updatePackageDigitalCodes = (packageIndex: number, codesText: string) => {
    const codes = codesText
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
      .map((key, index) => ({
        id: `${Date.now()}-${index}`,
        key,
        used: false,
        assignedToOrderId: null,
      }))

    setFormData(prev => ({
      ...prev,
      packages: prev.packages?.map((pkg, i) =>
        i === packageIndex
          ? {
              ...pkg,
              digitalCodes: codes,
              hasDigitalCodes: codes.length > 0,
              has_digital_codes: codes.length > 0,
              availableCodesCount: codes.length,
              // When digital codes are added, disable manual inventory tracking completely
              // When digital codes are removed, restore to unlimited stock by default
              track_inventory: codes.length > 0 ? false : true,
              unlimited_stock: codes.length > 0 ? false : true,
              manual_quantity: codes.length > 0 ? 0 : 0,
            }
          : pkg,
      ) || [],
    }))
  }

  const getPackageDigitalCodesText = (pkg: Package) => {
    return pkg.digitalCodes?.map((code) => code.key).join("\n") || ""
  }

  // Pricing calculation helpers
  const calculateDiscount = (originalPrice: number, discountedPrice: number) => {
    if (originalPrice <= 0 || discountedPrice >= originalPrice) return 0
    return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100)
  }



  const validatePricing = (original: number, user: number, discount?: number, distributor?: number) => {
    const errors: string[] = []

    if (user <= original) {
      errors.push("سعر المستخدم يجب أن يكون أكبر من السعر الأصلي")
    }

    if (discount !== undefined) {
      if (discount >= user) {
        errors.push("سعر الخصم يجب أن يكون أقل من سعر المستخدم")
      }
      if (discount <= original) {
        errors.push("سعر الخصم يجب أن يكون أكبر من السعر الأصلي")
      }
      if (discount === user) {
        errors.push("سعر الخصم لا يمكن أن يساوي سعر المستخدم")
      }
    }

    if (distributor !== undefined) {
      if (distributor >= user) {
        errors.push("سعر الموزع يجب أن يكون أقل من سعر المستخدم")
      }
      if (distributor === user) {
        errors.push("سعر الموزع لا يمكن أن يساوي سعر المستخدم")
      }
      if (distributor <= original) {
        errors.push("سعر الموزع يجب أن يكون أكبر من السعر الأصلي")
      }
    }

    return errors
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!formData.title || !formData.description || !formData.cover_image) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    // Validate pricing based on whether packages exist
    if (!formData.packages || formData.packages.length === 0) {
      // Product-level pricing validation
      if (!formData.original_price || !formData.user_price) {
        alert('يجب تحديد أسعار المنتج عند عدم وجود حزم')
        return
      }

      const pricingErrors = validatePricing(
        formData.original_price,
        formData.user_price,
        formData.discount_price,
        formData.distributor_price
      )

      if (pricingErrors.length > 0) {
        alert('أخطاء في التسعير:\n' + pricingErrors.join('\n'))
        return
      }
    } else {
      // Package pricing validation
      for (let i = 0; i < formData.packages.length; i++) {
        const pkg = formData.packages[i]
        if (!pkg.name || !pkg.original_price || !pkg.user_price) {
          alert(`يرجى ملء جميع حقول الحزمة ${i + 1}`)
          return
        }

        const pricingErrors = validatePricing(
          pkg.original_price,
          pkg.user_price,
          pkg.discount_price,
          pkg.distributor_price
        )

        if (pricingErrors.length > 0) {
          alert(`أخطاء في تسعير الحزمة "${pkg.name}":\n` + pricingErrors.join('\n'))
          return
        }
      }
    }

    // Prepare the data with custom fields and dropdowns
    const productData = {
      ...formData,
      customFields: productCustomFields,
      dropdowns: productDropdowns
    }

    const result = modalMode === 'edit' && editingProduct
      ? await updateItem(editingProduct.id, productData)
      : await createItem(productData)

    if (result.success) {
      closeModal()
    }
  }

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl md:text-2xl font-bold">إدارة المنتجات</h2>
        <button
          onClick={openCreateModal}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          إضافة منتج
        </button>
      </div>

      {/* Filters */}
      <AdminFilters
        filters={filters}
        onFiltersChange={updateFilters}
        filterOptions={filterOptions}
        searchPlaceholder="البحث في المنتجات..."
      />

      {/* Table */}
      <AdminTable
        items={products}
        columns={columns}
        loading={loading}
        actionLoading={actionLoading}
        onEdit={openEditModal}
        onDelete={deleteItem}
        renderMobileCard={renderMobileCard}
        emptyState={{
          title: "لا توجد منتجات",
          description: "ابدأ بإضافة منتج جديد",
          action: (
            <button onClick={openCreateModal} className="btn-primary">
              إضافة منتج جديد
            </button>
          )
        }}
      />

      {/* Pagination */}
      <AdminPagination
        pagination={pagination}
        onPageChange={goToPage}
        onNextPage={nextPage}
        onPrevPage={prevPage}
        entityName="منتج"
      />

      {/* Product Modal */}
      <AdminModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalMode === 'edit' ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        size="xl"
      >
        <div className="space-y-4 md:space-y-6">
          {/* Tabs Navigation */}
          <div className="border-b border-gray-600/50">
            <nav className="flex space-x-2 md:space-x-8 rtl:space-x-reverse overflow-x-auto scrollbar-hide">
              {[
                { id: 'basic', label: 'المعلومات الأساسية', shortLabel: 'أساسي', icon: Package },
                { id: 'pricing', label: 'التسعير', shortLabel: 'تسعير', icon: Key },
                { id: 'packages', label: 'الحزم', shortLabel: 'حزم', icon: Package },
                { id: 'fields', label: 'الحقول المخصصة', shortLabel: 'حقول', icon: Settings }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse py-2 md:py-3 px-2 md:px-3 border-b-2 font-medium text-xs md:text-sm transition-colors whitespace-nowrap min-h-[44px] touch-target ${
                      activeTab === tab.id
                        ? 'border-purple-500 text-purple-400'
                        : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 flex-shrink-0" />
                    <span className="hidden md:inline">{tab.label}</span>
                    <span className="md:hidden">{tab.shortLabel}</span>
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Basic Info Tab */}
          {activeTab === 'basic' && (
            <ProductBasicForm
              formData={formData}
              setFormData={setFormData}
              categories={categories}
              categoriesLoading={categoriesLoading}
            />
          )}

          {/* Pricing Tab */}
          {activeTab === 'pricing' && (
            <div className="space-y-4 md:space-y-6">
              {/* Product-Level Pricing */}
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6">
                <div className="flex items-center space-x-2 space-x-reverse mb-4">
                  <Key className="w-5 h-5 text-blue-400" />
                  <h3 className="text-lg font-medium text-blue-400">تسعير المنتج</h3>
                </div>

                <div className="text-sm text-gray-300 mb-4">
                  <p className="mb-2">يُستخدم عند عدم وجود حزم للمنتج:</p>
                  <ul className="text-xs text-gray-400 space-y-1">
                    <li>• السعر الأصلي: تكلفة المنتج الأساسية</li>
                    <li>• سعر المستخدم: السعر النهائي للعملاء</li>
                    <li>• سعر الخصم: سعر مخفض اختياري</li>
                    <li>• سعر الموزع: سعر خاص للموزعين</li>
                  </ul>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-xs font-medium mb-1 text-gray-300">السعر الأصلي (USD)</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.original_price || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, original_price: Number(e.target.value) || undefined }))}
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium mb-1 text-gray-300">سعر المستخدم (USD)</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.user_price || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, user_price: Number(e.target.value) || undefined }))}
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium mb-1 text-gray-300">سعر الخصم (USD)</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.discount_price || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, discount_price: Number(e.target.value) || undefined }))}
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium mb-1 text-gray-300">سعر الموزع (USD)</label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.distributor_price || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, distributor_price: Number(e.target.value) || undefined }))}
                      className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="0.00"
                    />
                  </div>
                </div>

                {/* Pricing Calculations */}
                {formData.original_price && formData.user_price && (
                  <div className="mt-4 p-4 bg-gray-700/30 rounded-lg">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-400 mb-1">ربح المستخدم:</p>
                        <p className="font-medium text-green-400">
                          {calculateProfitMargin(formData.original_price, formData.user_price).toFixed(2)} USD
                        </p>
                      </div>

                      {formData.discount_price && (
                        <div>
                          <p className="text-gray-400 mb-1">ربح الخصم:</p>
                          <p className="font-medium text-green-400">
                            {calculateProfitMargin(formData.original_price, formData.discount_price).toFixed(2)} USD
                          </p>
                        </div>
                      )}

                      {formData.distributor_price && (
                        <div>
                          <p className="text-gray-400 mb-1">ربح الموزع:</p>
                          <p className="font-medium text-green-400">
                            {calculateProfitMargin(formData.original_price, formData.distributor_price).toFixed(2)} USD
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Packages Tab */}
          {activeTab === 'packages' && (
            <div className="space-y-4 md:space-y-6">
              {/* Packages Management */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">حزم المنتج</h3>
                  <button
                    type="button"
                    onClick={addPackage}
                    className="btn-secondary flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    إضافة حزمة
                  </button>
                </div>

                {(!formData.packages || formData.packages.length === 0) && (
                  <div className="text-center py-8 bg-gray-700/30 rounded-lg border border-gray-600/50">
                    <Package className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-400 mb-4">لا توجد حزم للمنتج</p>
                    <button
                      type="button"
                      onClick={addPackage}
                      className="btn-primary"
                    >
                      إضافة حزمة جديدة
                    </button>
                  </div>
                )}

                <div className="space-y-6">
                  {formData.packages?.map((pkg, index) => (
                    <div key={index} className="bg-gray-700/30 rounded-lg p-6 border border-gray-600/50">
                      {/* Package Basic Info */}
                      <div className="space-y-4 mb-4">
                        <input
                          type="text"
                          value={pkg.name}
                          onChange={(e) => updatePackage(index, "name", e.target.value)}
                          placeholder="اسم الحزمة *"
                          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                        />

                        {/* Enhanced Pricing Fields */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div>
                            <label className="block text-xs font-medium mb-1 text-gray-300">السعر الأصلي (USD) *</label>
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={pkg.original_price || ""}
                              onChange={(e) => updatePackage(index, "original_price", Number(e.target.value) || 0)}
                              placeholder="0.00"
                              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                            />
                          </div>

                          <div>
                            <label className="block text-xs font-medium mb-1 text-gray-300">سعر المستخدم (USD) *</label>
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={pkg.user_price || ""}
                              onChange={(e) => updatePackage(index, "user_price", Number(e.target.value) || 0)}
                              placeholder="0.00"
                              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                            />
                          </div>

                          <div>
                            <label className="block text-xs font-medium mb-1 text-gray-300">سعر الخصم (USD)</label>
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={pkg.discount_price || ""}
                              onChange={(e) => updatePackage(index, "discount_price", Number(e.target.value) || undefined)}
                              placeholder="0.00"
                              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                            />
                          </div>

                          <div>
                            <label className="block text-xs font-medium mb-1 text-gray-300">سعر الموزع (USD)</label>
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={pkg.distributor_price || ""}
                              onChange={(e) => updatePackage(index, "distributor_price", Number(e.target.value) || undefined)}
                              placeholder="0.00"
                              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                            />
                          </div>
                        </div>

                        {/* Real-time Pricing Calculations */}
                        {pkg.original_price && pkg.user_price && (
                          <div className="p-3 bg-gray-700/30 rounded-lg">
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-gray-400 mb-1">ربح المستخدم:</p>
                                <p className="font-medium text-green-400">
                                  {calculateProfitMargin(pkg.original_price, pkg.user_price).toFixed(2)} USD
                                </p>
                              </div>

                              {pkg.discount_price && (
                                <div>
                                  <p className="text-gray-400 mb-1">ربح الخصم:</p>
                                  <p className="font-medium text-green-400">
                                    {calculateProfitMargin(pkg.original_price, pkg.discount_price).toFixed(2)} USD
                                  </p>
                                </div>
                              )}

                              {pkg.distributor_price && (
                                <div>
                                  <p className="text-gray-400 mb-1">ربح الموزع:</p>
                                  <p className="font-medium text-green-400">
                                    {calculateProfitMargin(pkg.original_price, pkg.distributor_price).toFixed(2)} USD
                                  </p>
                                </div>
                              )}

                              {pkg.discount_price && (
                                <div>
                                  <p className="text-gray-400 mb-1">نسبة الخصم:</p>
                                  <p className="font-medium text-orange-400">
                                    {calculateDiscount(pkg.user_price, pkg.discount_price)}%
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Package Image Options */}
                      <div className="space-y-4 mb-4">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={pkg.use_product_image || false}
                              onChange={(e) => {
                                updatePackage(index, "use_product_image", e.target.checked)
                                if (e.target.checked) {
                                  updatePackage(index, "image_reference_type", "product_image")
                                  updatePackage(index, "image", "")
                                }
                              }}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                          </label>
                          <span className="text-sm text-gray-300">استخدام صورة المنتج</span>
                        </div>

                        {!pkg.use_product_image && (
                          <input
                            type="url"
                            value={pkg.image || ""}
                            onChange={(e) => updatePackage(index, "image", e.target.value)}
                            placeholder="رابط صورة الحزمة"
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                          />
                        )}

                        <textarea
                          value={pkg.description || ""}
                          onChange={(e) => updatePackage(index, "description", e.target.value)}
                          placeholder="وصف الحزمة (اختياري)"
                          rows={2}
                          className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 resize-none"
                        />
                      </div>

                      {/* Digital Codes Section */}
                      <div className="space-y-4">
                        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                          <div className="flex items-center space-x-2 space-x-reverse mb-3">
                            <Key className="w-5 h-5 text-blue-400" />
                            <h5 className="font-medium text-blue-400">الأكواد الرقمية</h5>
                          </div>

                          <div className="text-sm text-gray-300 mb-4">
                            <p className="mb-2">إضافة أكواد رقمية للتسليم الفوري:</p>
                            <ul className="text-xs text-gray-400 space-y-1">
                              <li>• كود واحد في كل سطر</li>
                              <li>• سيتم تخصيص كود واحد لكل طلب</li>
                              <li>• إذا نفدت الأكواد، ستصبح الحزمة غير متاحة</li>
                            </ul>
                          </div>

                          <textarea
                            value={getPackageDigitalCodesText(pkg)}
                            onChange={(e) => updatePackageDigitalCodes(index, e.target.value)}
                            placeholder="أدخل الأكواد الرقمية (كود واحد في كل سطر)&#10;مثال:&#10;AB12-XY34-ZZ78&#10;CD56-PL90-QW12&#10;9GHT-LMK3-992Z"
                            rows={4}
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 font-mono text-sm resize-none"
                          />

                          {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                            <div className="mt-3 text-sm">
                              <span className="text-green-400">✓ {pkg.digitalCodes.length} كود متاح</span>
                            </div>
                          )}
                        </div>

                        {/* Manual Inventory Management Section - Only show when no digital codes */}
                        {!(pkg.hasDigitalCodes || pkg.has_digital_codes || (pkg.digitalCodes && pkg.digitalCodes.length > 0)) && (
                          <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                            <div className="flex items-center space-x-2 space-x-reverse mb-3">
                              <Package className="w-5 h-5 text-green-400" />
                              <h5 className="font-medium text-green-400">إدارة المخزون اليدوي</h5>
                            </div>

                            <div className="text-sm text-gray-300 mb-4">
                              <p className="mb-2">إدارة مخزون المنتجات الفيزيائية:</p>
                              <ul className="text-xs text-gray-400 space-y-1">
                                <li>• اختر بين مخزون غير محدود أو كمية محددة</li>
                                <li>• سيتم خصم الكمية تلقائياً عند الطلب</li>
                                <li>• المخزون غير المحدود مناسب للمنتجات الرقمية</li>
                              </ul>
                            </div>

                            <div className="space-y-4">
                              {/* Inventory Type Selection */}
                              <div className="space-y-3">
                                {/* Unlimited Stock Option */}
                                <div className="flex items-center space-x-3 space-x-reverse p-3 bg-gray-700/30 rounded-lg border border-gray-600/30">
                                  <label className="relative inline-flex items-center cursor-pointer">
                                    <input
                                      type="radio"
                                      name={`inventory-type-${index}`}
                                      checked={pkg.unlimited_stock || false}
                                      onChange={() => {
                                        updatePackage(index, "unlimited_stock", true)
                                        updatePackage(index, "manual_quantity", 0)
                                        updatePackage(index, "track_inventory", true)
                                      }}
                                      className="sr-only peer"
                                    />
                                    <div className="w-5 h-5 bg-gray-600 border-2 border-gray-500 rounded-full peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300/20 peer-checked:bg-green-600 peer-checked:border-green-600 flex items-center justify-center">
                                      {pkg.unlimited_stock && (
                                        <div className="w-2 h-2 bg-white rounded-full"></div>
                                      )}
                                    </div>
                                  </label>
                                  <div className="flex-1">
                                    <span className="text-sm font-medium text-gray-300">مخزون غير محدود</span>
                                    <p className="text-xs text-gray-400 mt-1">مناسب للمنتجات الرقمية أو الخدمات</p>
                                  </div>
                                </div>

                                {/* Limited Stock Option */}
                                <div className="flex items-center space-x-3 space-x-reverse p-3 bg-gray-700/30 rounded-lg border border-gray-600/30">
                                  <label className="relative inline-flex items-center cursor-pointer">
                                    <input
                                      type="radio"
                                      name={`inventory-type-${index}`}
                                      checked={!pkg.unlimited_stock}
                                      onChange={() => {
                                        updatePackage(index, "unlimited_stock", false)
                                        updatePackage(index, "track_inventory", true)
                                        if ((pkg.manual_quantity || 0) === 0) {
                                          updatePackage(index, "manual_quantity", 100)
                                        }
                                      }}
                                      className="sr-only peer"
                                    />
                                    <div className="w-5 h-5 bg-gray-600 border-2 border-gray-500 rounded-full peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300/20 peer-checked:bg-green-600 peer-checked:border-green-600 flex items-center justify-center">
                                      {!pkg.unlimited_stock && (
                                        <div className="w-2 h-2 bg-white rounded-full"></div>
                                      )}
                                    </div>
                                  </label>
                                  <div className="flex-1">
                                    <span className="text-sm font-medium text-gray-300">كمية محددة</span>
                                    <p className="text-xs text-gray-400 mt-1">مناسب للمنتجات الفيزيائية المحدودة</p>
                                  </div>
                                </div>
                              </div>

                              {/* Manual Quantity Input - Only show when limited stock is selected */}
                              {!pkg.unlimited_stock && (
                                <div className="mt-4">
                                  <label className="block text-sm font-medium mb-2 text-gray-300">الكمية المتاحة</label>
                                  <input
                                    type="number"
                                    min="0"
                                    value={pkg.manual_quantity || 0}
                                    onChange={(e) => updatePackage(index, "manual_quantity", Math.max(0, Number(e.target.value) || 0))}
                                    placeholder="أدخل الكمية المتاحة"
                                    className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-500/20"
                                  />
                                </div>
                              )}

                              {/* Inventory Status Display */}
                              <div className="mt-4 p-3 bg-gray-700/30 rounded-lg border border-gray-600/30">
                                <p className="text-xs font-medium mb-2 text-gray-300">حالة المخزون الحالية:</p>
                                {pkg.unlimited_stock ? (
                                  <div className="flex items-center space-x-2 space-x-reverse">
                                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                    <span className="text-green-400 text-sm font-medium">مخزون غير محدود</span>
                                  </div>
                                ) : (
                                  <div className="flex items-center space-x-2 space-x-reverse">
                                    <div className={`w-2 h-2 rounded-full ${(pkg.manual_quantity || 0) > 10 ? 'bg-green-400' : (pkg.manual_quantity || 0) > 0 ? 'bg-orange-400' : 'bg-red-400'}`}></div>
                                    <span className={`text-sm font-medium ${(pkg.manual_quantity || 0) > 10 ? 'text-green-400' : (pkg.manual_quantity || 0) > 0 ? 'text-orange-400' : 'text-red-400'}`}>
                                      {(pkg.manual_quantity || 0) > 0 ? `${pkg.manual_quantity} قطعة متاحة` : 'نفد المخزون'}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Remove Package Button */}
                      <div className="flex items-center justify-end mt-4">
                        <button
                          type="button"
                          onClick={() => removePackage(index)}
                          className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-colors"
                          title="حذف الحزمة"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Custom Fields Tab */}
          {activeTab === 'fields' && (
            <div className="space-y-4 md:space-y-6">
              {/* Product-Level Custom Fields */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium">الحقول المخصصة</h3>
                    <p className="text-sm text-gray-400">حقول إضافية يملؤها العميل عند الطلب</p>
                  </div>
                </div>

                <div className="mb-4">
                  <button
                    type="button"
                    onClick={addProductCustomField}
                    className="btn-secondary flex items-center space-x-2 rtl:space-x-reverse"
                  >
                    <Plus className="w-4 h-4" />
                    <span>إضافة حقل مخصص</span>
                  </button>
                </div>

                <div className="space-y-4">
                  {productCustomFields.map((field, index) => (
                    <div key={index} className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-300">تسمية الحقل *</label>
                          <input
                            type="text"
                            value={field.label}
                            onChange={(e) => updateProductCustomField(index, 'label', e.target.value)}
                            placeholder="مثال: البريد الإلكتروني"
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-300">نوع الحقل *</label>
                          <select
                            value={field.field_type}
                            onChange={(e) => updateProductCustomField(index, 'field_type', e.target.value)}
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                          >
                            <option value="text">نص</option>
                            <option value="email">بريد إلكتروني</option>
                            <option value="password">كلمة مرور</option>
                            <option value="number">رقم</option>
                            <option value="tel">هاتف</option>
                            <option value="url">رابط</option>
                            <option value="textarea">نص طويل</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-300">النص التوضيحي</label>
                          <input
                            type="text"
                            value={field.placeholder || ''}
                            onChange={(e) => updateProductCustomField(index, 'placeholder', e.target.value)}
                            placeholder="مثال: أدخل بريدك الإلكتروني"
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                          />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-300">وصف الحقل</label>
                          <textarea
                            value={field.description || ''}
                            onChange={(e) => updateProductCustomField(index, 'description', e.target.value)}
                            placeholder="وصف مفصل لغرض هذا الحقل..."
                            rows={3}
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 resize-none"
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <input
                              type="checkbox"
                              checked={field.required}
                              onChange={(e) => updateProductCustomField(index, 'required', e.target.checked)}
                              className="rounded"
                            />
                            <label className="text-sm text-gray-300">حقل مطلوب</label>
                          </div>

                          <button
                            type="button"
                            onClick={() => removeProductCustomField(index)}
                            className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-colors"
                            title="حذف الحقل"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}

                  {productCustomFields.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      <Settings className="w-12 h-12 mx-auto mb-3 opacity-50" />
                      <p>لا توجد حقول مخصصة</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Product-Level Dropdowns */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium">القوائم المنسدلة</h3>
                    <p className="text-sm text-gray-400">قوائم اختيار للعميل (مثل: نوع الخدمة، المنطقة)</p>
                  </div>
                </div>

                <div className="mb-4">
                  <button
                    type="button"
                    onClick={addProductDropdown}
                    className="btn-secondary flex items-center space-x-2 rtl:space-x-reverse"
                  >
                    <Plus className="w-4 h-4" />
                    <span>إضافة قائمة منسدلة</span>
                  </button>
                </div>

                <div className="space-y-4">
                  {productDropdowns.map((dropdown, dropdownIndex) => (
                    <div key={dropdownIndex} className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-300">تسمية القائمة *</label>
                          <input
                            type="text"
                            value={dropdown.label}
                            onChange={(e) => updateProductDropdown(dropdownIndex, 'label', e.target.value)}
                            placeholder="مثال: نوع الخدمة"
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium mb-1 text-gray-300">وصف القائمة</label>
                          <input
                            type="text"
                            value={dropdown.description || ''}
                            onChange={(e) => updateProductDropdown(dropdownIndex, 'description', e.target.value)}
                            placeholder="وصف القائمة المنسدلة..."
                            className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                          />
                        </div>
                      </div>

                      {/* Dropdown Options */}
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-3">
                          <label className="block text-sm font-medium">خيارات القائمة</label>
                          <button
                            type="button"
                            onClick={() => addDropdownOption(dropdownIndex)}
                            className="text-sm text-purple-400 hover:text-purple-300 flex items-center space-x-1 rtl:space-x-reverse"
                          >
                            <Plus className="w-3 h-3" />
                            <span>إضافة خيار</span>
                          </button>
                        </div>

                        <div className="space-y-3">
                          <div className="grid grid-cols-12 gap-2 text-xs text-gray-400 px-1">
                            <div className="col-span-8">النص للعميل</div>
                            <div className="col-span-2">افتراضي</div>
                            <div className="col-span-2">إجراءات</div>
                          </div>

                          {dropdown.options.map((option, optionIndex) => (
                            <div key={optionIndex} className="grid grid-cols-12 gap-2 items-center">
                              <input
                                type="text"
                                value={option.label}
                                onChange={(e) => updateDropdownOption(dropdownIndex, optionIndex, 'label', e.target.value)}
                                placeholder="النسخة العالمية"
                                className="col-span-8 bg-gray-700/50 border border-gray-600/50 rounded px-3 py-2 text-sm focus:outline-none focus:border-purple-500"
                                title="النص الذي سيراه العميل في القائمة"
                              />
                              <div className="col-span-2 flex justify-center">
                                <label className="flex items-center space-x-1 rtl:space-x-reverse" title="هذا الخيار سيكون مختاراً تلقائياً">
                                  <input
                                    type="checkbox"
                                    checked={option.is_default}
                                    onChange={(e) => {
                                      // Only allow one default option
                                      if (e.target.checked) {
                                        // Clear other defaults first
                                        dropdown.options.forEach((_, i) => {
                                          if (i !== optionIndex) {
                                            updateDropdownOption(dropdownIndex, i, 'is_default', false)
                                          }
                                        })
                                      }
                                      updateDropdownOption(dropdownIndex, optionIndex, 'is_default', e.target.checked)
                                    }}
                                    className="rounded"
                                  />
                                  <span className="text-xs">✓</span>
                                </label>
                              </div>
                              <div className="col-span-2 flex justify-center">
                                <button
                                  type="button"
                                  onClick={() => removeDropdownOption(dropdownIndex, optionIndex)}
                                  className="text-red-400 hover:text-red-300 p-1 rounded hover:bg-red-400/10"
                                  title="حذف هذا الخيار"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <input
                            type="checkbox"
                            checked={dropdown.required}
                            onChange={(e) => updateProductDropdown(dropdownIndex, 'required', e.target.checked)}
                            className="rounded"
                          />
                          <label className="text-sm text-gray-300">قائمة مطلوبة</label>
                        </div>

                        <button
                          type="button"
                          onClick={() => removeProductDropdown(dropdownIndex)}
                          className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-400/10 transition-colors"
                          title="حذف القائمة"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}

                  {productDropdowns.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      <List className="w-12 h-12 mx-auto mb-3 opacity-50" />
                      <p>لا توجد قوائم منسدلة</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50">
            <button
              onClick={handleSubmit}
              disabled={actionLoading === 'create'}
              className="flex-1 btn-primary flex items-center justify-center gap-2"
            >
              {actionLoading === 'create' ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b border-white"></div>
              ) : (
                <Save className="w-4 h-4" />
              )}
              {modalMode === 'edit' ? 'تحديث المنتج' : 'إضافة المنتج'}
            </button>
            <button
              onClick={closeModal}
              disabled={actionLoading === 'create'}
              className="flex-1 btn-secondary"
            >
              إلغاء
            </button>
          </div>
        </div>
      </AdminModal>
    </div>
  )
}

